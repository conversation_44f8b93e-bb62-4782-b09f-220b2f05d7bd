<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>表情包模板列表</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            background: #f5f7fa;
            margin: 0;
            padding: 20px;
            color: #333;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0 0 10px 0;
            font-size: 24px;
        }
        
        .stats {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-top: 15px;
        }
        
        .stat-item {
            text-align: center;
        }
        
        .stat-number {
            font-size: 20px;
            font-weight: bold;
            display: block;
        }
        
        .stat-label {
            font-size: 12px;
            opacity: 0.9;
        }
        
        .content {
            padding: 20px;
        }
        
        .category-section {
            margin-bottom: 25px;
        }
        
        .category-title {
            font-size: 16px;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 10px;
            padding-bottom: 5px;
            border-bottom: 2px solid #667eea;
        }
        
        .meme-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 10px;
        }
        
        .meme-item {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 12px;
            transition: all 0.2s ease;
        }
        
        .meme-item:hover {
            background: #e3f2fd;
            border-color: #667eea;
        }
        
        .meme-name {
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }
        
        .meme-keywords {
            color: #666;
            font-size: 13px;
            margin-bottom: 5px;
        }
        
        .meme-params {
            font-size: 12px;
            color: #888;
        }
        
        .status-enabled {
            color: #28a745;
        }
        
        .status-disabled {
            color: #dc3545;
        }
        
        .footer {
            background: #f8f9fa;
            padding: 15px;
            text-align: center;
            color: #666;
            font-size: 13px;
        }
        
        .usage-tip {
            background: #e8f5e8;
            border: 1px solid #c3e6c3;
            border-radius: 5px;
            padding: 10px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎭 表情包模板列表</h1>
            <div class="stats">
                <div class="stat-item">
                    <span class="stat-number">{{ total_memes }}</span>
                    <span class="stat-label">总模板</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">{{ enabled_memes }}</span>
                    <span class="stat-label">可用</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">{{ categories|length }}</span>
                    <span class="stat-label">分类</span>
                </div>
            </div>
        </div>
        
        <div class="content">
            {% if categories %}
                {% for category in categories[:3] %}
                <div class="category-section">
                    <div class="category-title">📁 {{ category }}</div>
                    <div class="meme-list">
                        {% for meme in memes if category in meme.tags %}
                            {% if loop.index <= 6 %}
                            <div class="meme-item">
                                <div class="meme-name">
                                    <span class="status-enabled">✅</span> {{ meme.name }}
                                </div>
                                <div class="meme-keywords">
                                    关键词: {{ meme.keywords[:3]|join(', ') }}{% if meme.keywords|length > 3 %}...{% endif %}
                                </div>
                                <div class="meme-params">
                                    {% if meme.max_images > 0 %}
                                        📷 {{ meme.min_images }}{% if meme.min_images != meme.max_images %}-{{ meme.max_images }}{% endif %}图
                                    {% endif %}
                                    {% if meme.max_texts > 0 %}
                                        💬 {{ meme.min_texts }}{% if meme.min_texts != meme.max_texts %}-{{ meme.max_texts }}{% endif %}文
                                    {% endif %}
                                </div>
                            </div>
                            {% endif %}
                        {% endfor %}
                    </div>
                </div>
                {% endfor %}
            {% endif %}
            
            <div class="category-section">
                <div class="category-title">📝 其他模板</div>
                <div class="meme-list">
                    {% for meme in memes if not meme.tags %}
                        {% if loop.index <= 8 %}
                        <div class="meme-item">
                            <div class="meme-name">
                                <span class="status-enabled">✅</span> {{ meme.name }}
                            </div>
                            <div class="meme-keywords">
                                关键词: {{ meme.keywords[:3]|join(', ') }}{% if meme.keywords|length > 3 %}...{% endif %}
                            </div>
                            <div class="meme-params">
                                {% if meme.max_images > 0 %}
                                    📷 {{ meme.min_images }}{% if meme.min_images != meme.max_images %}-{{ meme.max_images }}{% endif %}图
                                {% endif %}
                                {% if meme.max_texts > 0 %}
                                    💬 {{ meme.min_texts }}{% if meme.min_texts != meme.max_texts %}-{{ meme.max_texts }}{% endif %}文
                                {% endif %}
                            </div>
                        </div>
                        {% endif %}
                    {% endfor %}
                </div>
            </div>
            
            <div class="usage-tip">
                💡 <strong>使用方法:</strong> 发送 "关键词 + 图片/文字" 即可生成表情包<br>
                📝 <strong>查看详情:</strong> 使用 "/模板信息 关键词" 查看具体用法
            </div>
        </div>
        
        <div class="footer">
            共 {{ total_memes }} 个模板，显示部分内容 | 使用 /表情列表 查看完整列表
        </div>
    </div>
</body>
</html>
