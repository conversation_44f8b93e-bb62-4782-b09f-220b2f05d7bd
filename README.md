<div align="center">

![:name](https://count.getloli.com/@astrbot_plugin_meme_generator?name=astrbot_plugin_meme_generator&theme=gelbooru&padding=8&offset=0&align=top&scale=1&pixelated=0&darkmode=auto)

# 🎨 AstrBot 表情包生成器

_✨ 基于 [AstrBot](https://github.com/AstrBotDevs/AstrBot) 的智能表情包创作工具 ✨_

[![License](https://img.shields.io/badge/License-Apache%202.0-red.svg)](https://opensource.org/licenses/Apache-2.0)
[![Python 3.11+](https://img.shields.io/badge/Python-3.11%2B-brightgreen.svg)](https://www.python.org/)
[![AstrBot](https://img.shields.io/badge/AstrBot-3.5%2B-purple.svg)](https://github.com/Soulter/AstrBot)
[![GitHub](https://img.shields.io/badge/开发者-SodaSizzle-orange)](http://127.0.0.1:3000/SodaSizzle)

</div>

## 🚀 项目简介

这是一个为 AstrBot 量身定制的**智能表情包生成插件**，采用先进的图像处理算法和模板匹配技术，为用户提供丰富多样的表情包创作体验。

### ✨ 核心特性

- 🎯 **智能关键词识别** - 支持模糊匹配和精确匹配两种模式
- 🖼️ **多源图片支持** - 自动获取用户头像、支持上传图片、引用消息图片
- ⚡ **高性能渲染** - 基于 Rust 底层引擎，生成速度极快
- 🎨 **丰富模板库** - 内置 200+ 精选表情包模板
- 🔧 **灵活配置** - 支持黑名单管理、图片压缩、超时控制等
- 📱 **跨平台兼容** - 完美适配各种聊天平台

### 🚀 v1.1.0 新特性

- 🎯 **权限系统优化** - 使用框架原生权限过滤器，支持群聊管理员操作
- 🌐 **群聊管理支持** - 管理员命令可在群聊中使用，无需私聊限制
- 🔧 **代码简化** - 移除冗余权限检查，提升代码可维护性
- 📋 **优化列表展示** - 禁用列表支持格式化显示，包含编号、分页和统计信息
- 💾 **头像缓存系统** - 智能缓存用户头像，显著提升生成速度
- ⏱️ **用户冷却机制** - 防止频繁请求，保护服务器资源
- ⏰ **生成超时控制** - 避免长时间等待，提升用户体验
- 🏗️ **模块化架构** - 代码结构清晰，易于维护和扩展
- 🔧 **缓存清理优化** - 修复缓存清理间隔配置问题，现在正确使用配置文件中的时间设置

## 📦 快速安装

### 方式一：自动安装（推荐）

1. 打开 AstrBot 管理面板
2. 进入插件市场，搜索 `astrbot_plugin_meme_generator`
3. 点击安装，等待自动完成

### 方式二：手动部署

```bash
# 进入插件目录
cd /path/to/AstrBot/data/plugins

# 克隆项目
git clone http://127.0.0.1:3000/SodaSizzle/astrbot_plugin_meme_generator

# 安装依赖
pip install -r astrbot_plugin_meme_generator/requirements.txt
```

### Docker 环境配置

如果使用 Docker 部署，可能需要安装额外的系统依赖：

```dockerfile
# 在 Dockerfile 中添加
RUN apt-get update && apt-get install -y \
    libgl1-mesa-glx \
    libglib2.0-0 \
    libfontconfig1 \
    libxrender1 \
    libxtst6
```

### 🔧 初始化配置

插件首次启动时会自动：
- 下载必要的字体文件
- 初始化模板资源
- 创建配置文件

> 💡 **提示**: 首次启动可能需要几分钟时间下载资源，请耐心等待

## ⚙️ 配置说明

### 核心配置

在 AstrBot 管理面板中进行配置：`插件管理` → `astrbot_plugin_meme_generator` → `插件配置`

| 配置项 | 说明 | 默认值 | 重要程度 |
|--------|------|--------|----------|
| `enable_plugin` | 启用插件功能 | `true` | ⭐⭐⭐⭐⭐ |
| `smart_match` | 智能匹配模式 | `false` | ⭐⭐⭐⭐⭐ |
| `cooldown_seconds` | 用户冷却时间(秒) | `3` | ⭐⭐⭐⭐⭐ |
| `generation_timeout` | 生成超时时间(秒) | `30` | ⭐⭐⭐⭐ |
| `enable_avatar_cache` | 启用头像缓存 | `true` | ⭐⭐⭐⭐ |
| `cache_expire_hours` | 缓存过期时间(小时) | `24` | ⭐⭐⭐ |
| `auto_compress` | 自动图片压缩 | `true` | ⭐⭐⭐ |
| `disabled_templates` | 禁用模板列表 | `[]` | ⭐⭐ |
| `list_sort_method` | 列表排序方式 | `keywords_pinyin` | ⭐⭐ |
| `resource_check` | 启动资源检查 | `true` | ⭐ |

### 缓存系统说明

- **缓存位置**: `data/cache/meme_avatars/`
- **缓存文件**: 用户头像以MD5哈希命名存储
- **元数据**: `metadata.json` 记录缓存时间戳
- **自动清理**: 每6小时自动清理过期缓存

## 📋 命令列表

### 🎮 用户命令

| 命令 | 功能描述 | 示例 |
|------|----------|------|
| `表情列表` | 查看所有可用模板 | `表情列表` |
| `模板信息 <关键词>` | 查看模板详细信息 | `模板信息 摸头` |
| `<关键词> [参数]` | 生成表情包 | `摸头 @某人` |

### 🔧 Bot管理员命令

| 命令 | 功能描述 | 示例 | 权限要求 |
|------|----------|------|----------|
| `启用表情包` | 启用插件功能 | `启用表情包` | Bot管理员 |
| `禁用表情包` | 禁用插件功能 | `禁用表情包` | Bot管理员 |
| `表情包状态` | 查看插件状态 | `表情包状态` | Bot管理员 |
| `表情包信息` | 查看插件详细信息 | `表情包信息` | Bot管理员 |
| `禁用模板 <关键词>` | 禁用指定模板 | `禁用模板 摸头` | Bot管理员 |
| `启用模板 <关键词>` | 启用指定模板 | `启用模板 摸头` | Bot管理员 |
| `禁用列表` | 查看禁用模板列表 | `禁用列表` | Bot管理员 |

> 💡 **管理员权限**: 只有Bot管理员才能使用管理员命令，支持私聊和群聊
> 🌐 **全局控制**: 插件开关是全局的，不分群组

### 🎯 使用技巧

- **智能匹配**: 开启后，消息中包含关键词即可触发，如 "我想要摸头" 也能触发摸头表情包
- **参数自动补全**: 缺少参数时自动使用发送者头像和昵称
- **引用消息**: 引用他人消息后发送关键词，可使用被引用者的头像
- **冷却机制**: 避免频繁生成，保护服务器性能
- **功能开关**: Bot管理员可以通过私聊随时启用/禁用插件功能，全局生效
- **模板管理**: Bot管理员可以通过私聊禁用/启用特定模板，精确控制可用模板
- **列表管理**: 禁用列表支持格式化显示，包含编号和分页功能

### 🎯 热门关键词

#### 🔥 经典系列
```
摸头、pat、举牌、抱抱、亲亲、贴贴、蹭蹭、rua、捏脸
```

#### 😂 搞笑系列
```
王境泽、为所欲为、切格瓦拉、谁反对、喜报、鬼畜、小丑
```

#### 🎮 二次元系列
```
阿尼亚喜欢、蔚蓝档案标题、原神启动、可莉吃、胡桃啃、纳西妲啃
```

#### 💼 日常系列
```
打工人、悲报、加班、请假条、结婚申请、离婚协议、上香
```

#### 🎨 创意系列
```
字符画、像素化、三维旋转、万花镜、哈哈镜、波纹、震动
```

> 💡 **提示**: 使用 `表情列表` 命令可以查看完整的关键词列表和分类

## 🐔 使用说明

- 本插件支持从原始消息中提取参数，请用空格隔开参数，如 “喜报 nmsl”
- 本插件支持从引用消息中提取参数，如“[引用的消息] 喜报”
- 提供的参数不够时，插件自动获取消息发送者、被 @ 的用户以及 bot 自身的相关参数来补充。

## 📌 注意事项

- 缓存数据存储在 `data/cache/meme_avatars/` 目录中，可定期清理

## 🔧 技术细节

### v1.1.0 新增功能详解

#### 缓存清理间隔配置修复
**问题**: 之前版本中，缓存清理任务的间隔时间被硬编码为6小时，没有使用配置文件中的 `cache_expire_hours` 设置。

**解决方案**:
- 修改 `MemeManager` 初始化 `CacheManager` 时传入正确的配置参数
- 现在缓存清理间隔会正确使用配置文件中的 `cache_expire_hours` 值（默认24小时）
- 用户可以通过配置文件自定义缓存清理间隔时间

#### 格式化列表展示
**改进前**: 直接打印 Python 列表字符串 `['template1', 'template2']`

**改进后**: 格式化展示，包含统计信息和分页
```
🔒 禁用模板列表
📊 总计: 15 个模板
📄 分页显示 (每页 20 个，共 1 页)
──────────────────────────────
 1. template1
 2. template2
 3. template3
...
```

### v1.0.2 优化的关键问题

#### 配置持久化API优化
**问题**: 原版本使用 `save_config(replace_config=self.config)` 容易混淆，可能导致循环引用或框架不兼容。

**解决方案**:
```python
# 修复前 (有风险)
def save_config(self):
    self.config["disabled_templates"] = self.disabled_templates
    self.config.save_config(replace_config=self.config)  # ❌ 潜在循环引用

# 修复后 (安全)
def save_config(self):
    self.config["disabled_templates"] = self.disabled_templates
    self.config["enable_plugin"] = self.enable_plugin
    self.config.save_config()  # ✅ 只调用框架方法

def _save_specific_config(self, key: str, value):
    self.config[key] = value
    self.config.save_config()  # ✅ 专用方法，更清晰
```

### v1.0.1 修复的关键问题

#### 模板加载顺序问题
**问题**: 原版本中 `TemplateManager` 在构造时立即调用 `get_memes()`，而资源检查是异步触发的，导致资源首次未就绪时模板加载不全。

**解决方案**:
- 实现懒加载机制，模板按需加载
- 资源检查完成后自动刷新模板列表
- 使用异步锁防止并发加载问题
- 提供手动刷新功能

#### 变量作用域错误
**问题**: `UnboundLocalError: cannot access local variable 'asyncio'`

**解决方案**: 移除局部的 `import asyncio`，使用全局导入避免作用域冲突

#### 性能优化
- 启动时不强制加载模板，提升启动速度
- 初始化失败时支持后续重新加载
- 双重检查锁定模式避免重复加载

### 🚀 v1.1.0 更新内容

- 🔧 **缓存清理配置修复** - 修复缓存清理间隔硬编码问题，现在正确使用配置文件设置
- 🗑️ **移除模板预览功能** - 简化插件功能，移除模板预览相关的所有代码和配置
- ⚙️ **配置项优化** - 清理不再使用的配置项，简化配置文件结构
- 📝 **文档更新** - 更新README和帮助文档，移除预览相关说明

## 👥 贡献指南

- 🌟 Star 这个项目！（点右上角的星星，感谢支持！）
- 🐛 提交 Issue 报告问题
- 💡 提出新功能建议
- 🔧 提交 Pull Request 改进代码

## 🔗 相关项目

- [meme-generator-rs](https://github.com/MemeCrafters/meme-generator-rs) - 核心生成引擎
- [AstrBot](https://astrbot.app/) - 多平台聊天机器人框架
- [SodaSizzle's Projects](http://127.0.0.1:3000/SodaSizzle) - 更多有趣的项目

## 📄 许可证

本项目采用 Apache 2.0 许可证，详见 [LICENSE](LICENSE) 文件。

---

<div align="center">

**🎉 感谢使用 AstrBot 表情包生成器！**

如果觉得好用，请给个 ⭐ Star 支持一下！

</div>
