<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>禁用模板列表</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            min-height: 100vh;
            padding: 20px;
            color: #333;
        }
        
        .container {
            max-width: 900px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            color: white;
            padding: 40px 30px;
            text-align: center;
            position: relative;
        }
        
        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.1)"/><circle cx="10" cy="60" r="0.5" fill="rgba(255,255,255,0.1)"/><circle cx="90" cy="40" r="0.5" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
        }
        
        .header-content {
            position: relative;
            z-index: 1;
        }
        
        .header-icon {
            width: 80px;
            height: 80px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            font-size: 2.5em;
            backdrop-filter: blur(10px);
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header .subtitle {
            font-size: 1.1em;
            opacity: 0.9;
        }
        
        .stats {
            display: flex;
            justify-content: center;
            gap: 30px;
            margin-top: 20px;
        }
        
        .stat-item {
            text-align: center;
        }
        
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            display: block;
        }
        
        .stat-label {
            font-size: 0.9em;
            opacity: 0.8;
        }
        
        .content {
            padding: 40px 30px;
        }
        
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #666;
        }
        
        .empty-icon {
            font-size: 4em;
            margin-bottom: 20px;
            opacity: 0.5;
        }
        
        .empty-title {
            font-size: 1.5em;
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
        }
        
        .empty-message {
            font-size: 1.1em;
            line-height: 1.6;
        }
        
        .disabled-list {
            display: grid;
            gap: 15px;
        }
        
        .disabled-item {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.08);
            border-left: 4px solid #ff6b6b;
            display: flex;
            align-items: center;
            justify-content: space-between;
            transition: all 0.3s ease;
        }
        
        .disabled-item:hover {
            transform: translateX(5px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
        }
        
        .item-info {
            display: flex;
            align-items: center;
            gap: 15px;
            flex: 1;
        }
        
        .item-icon {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.2em;
            flex-shrink: 0;
        }
        
        .item-details {
            flex: 1;
        }
        
        .item-name {
            font-size: 1.2em;
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }
        
        .item-description {
            color: #666;
            font-size: 0.9em;
        }
        
        .item-status {
            background: #ffe6e6;
            color: #d63031;
            padding: 6px 12px;
            border-radius: 15px;
            font-size: 0.85em;
            font-weight: 500;
            border: 1px solid #ffb3b3;
        }
        
        .search-box {
            margin-bottom: 30px;
            text-align: center;
        }
        
        .search-input {
            width: 100%;
            max-width: 400px;
            padding: 15px 20px;
            border: 2px solid #e0e0e0;
            border-radius: 25px;
            font-size: 16px;
            outline: none;
            transition: all 0.3s ease;
        }
        
        .search-input:focus {
            border-color: #ff6b6b;
            box-shadow: 0 0 20px rgba(255, 107, 107, 0.2);
        }
        
        .footer {
            text-align: center;
            padding: 30px;
            background: #f8f9fa;
            color: #666;
        }
        
        .action-btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            background: linear-gradient(135deg, #00b894 0%, #00a085 100%);
            color: white;
            padding: 12px 24px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
            margin: 0 10px;
        }
        
        .action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0, 184, 148, 0.3);
        }
        
        .action-btn.secondary {
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
        }
        
        .action-btn.secondary:hover {
            box-shadow: 0 8px 20px rgba(116, 185, 255, 0.3);
        }
        
        @media (max-width: 768px) {
            .container {
                margin: 10px;
            }
            
            .content {
                padding: 20px 15px;
            }
            
            .header {
                padding: 30px 20px;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .stats {
                gap: 15px;
            }
            
            .stat-number {
                font-size: 1.5em;
            }
            
            .disabled-item {
                flex-direction: column;
                align-items: flex-start;
                gap: 15px;
            }
            
            .item-info {
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="header-content">
                <div class="header-icon">🔒</div>
                <h1>禁用模板列表</h1>
                <p class="subtitle">管理被禁用的表情包模板</p>
                <div class="stats">
                    <div class="stat-item">
                        <span class="stat-number">{{ disabled_count }}</span>
                        <span class="stat-label">禁用模板</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">{{ total_count - disabled_count }}</span>
                        <span class="stat-label">可用模板</span>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="content">
            {% if disabled_templates %}
            <div class="search-box">
                <input type="text" class="search-input" placeholder="🔍 搜索禁用的模板..." id="searchInput">
            </div>
            
            <div class="disabled-list" id="disabledList">
                {% for template in disabled_templates %}
                <div class="disabled-item" data-name="{{ template.lower() }}">
                    <div class="item-info">
                        <div class="item-icon">{{ loop.index }}</div>
                        <div class="item-details">
                            <div class="item-name">{{ template }}</div>
                            <div class="item-description">此模板已被管理员禁用</div>
                        </div>
                    </div>
                    <div class="item-status">已禁用</div>
                </div>
                {% endfor %}
            </div>
            {% else %}
            <div class="empty-state">
                <div class="empty-icon">🎉</div>
                <div class="empty-title">太棒了！</div>
                <div class="empty-message">
                    当前没有禁用的模板<br>
                    所有表情包模板都可以正常使用
                </div>
            </div>
            {% endif %}
        </div>
        
        <div class="footer">
            <a href="#" class="action-btn secondary">
                <span>📋</span>
                查看所有模板
            </a>
            <a href="#" class="action-btn">
                <span>⚙️</span>
                管理设置
            </a>
            <p style="margin-top: 20px;">
                💡 管理员可以使用 "/启用模板 模板名" 来重新启用被禁用的模板
            </p>
        </div>
    </div>

    <script>
        // 搜索功能
        document.getElementById('searchInput')?.addEventListener('input', function(e) {
            const searchTerm = e.target.value.toLowerCase();
            const items = document.querySelectorAll('.disabled-item');
            
            items.forEach(item => {
                const name = item.dataset.name;
                
                if (name.includes(searchTerm)) {
                    item.style.display = 'flex';
                } else {
                    item.style.display = 'none';
                }
            });
        });
    </script>
</body>
</html>
