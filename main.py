from astrbot.api.event import filter
from astrbot.api.star import Context, <PERSON>, register
from astrbot.core import AstrBotConfig
from astrbot.core.platform import AstrMessageEvent
from astrbot.core.star.filter.event_message_type import EventMessageType

from .config import MemeConfig
from .core import MemeManager
from .handlers import TemplateHandlers, GenerationHandler, AdminHandlers
from .utils import PermissionUtils


@register(
    "astrbot_plugin_meme_generator",
    "SodaSizzle",
    "高性能表情包生成器 - 支持多种模板和智能参数识别",
    "v1.1.0",
    "http://127.0.0.1:3000/SodaSizzle/astrbot_plugin_meme_generator",
)
class MemeGeneratorPlugin(Star):
    def __init__(self, context: Context, config: AstrBotConfig):
        super().__init__(context)

        # 初始化配置管理器
        self.meme_config = MemeConfig(config)

        # 初始化核心管理器
        self.meme_manager = MemeManager(self.meme_config)

        # 初始化命令处理器
        self.template_handlers = TemplateHandlers(self.meme_manager, self.meme_config)
        self.generation_handler = GenerationHandler(self.meme_manager)
        self.admin_handlers = AdminHandlers(self.meme_config)

    async def __aenter__(self):
        """异步上下文管理器入口"""
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口 - 清理资源"""
        await self.cleanup()
        return False  # 不抑制异常

    async def cleanup(self):
        """清理资源"""
        try:
            # 停止缓存清理任务
            await self.meme_manager.cache_manager.stop_cleanup_task()
        except Exception as e:
            from astrbot import logger
            logger.error(f"清理缓存管理器时出错: {e}")

    @filter.command("帮助菜单", alias={"meme帮助", "表情帮助", "help"})
    async def meme_help_menu(self, event: AstrMessageEvent):
        """查看meme插件帮助菜单"""
        # 检查插件是否启用
        if not self.meme_config.is_plugin_enabled():
            if PermissionUtils.is_bot_admin(event):
                yield event.plain_result(PermissionUtils.get_plugin_disabled_message())
            return

        # 自定义的 Jinja2 模板，支持 CSS
        MEME_HELP_TMPL = '''
        <!DOCTYPE html>
        <html lang="zh-CN">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <style>
                * {
                    margin: 0;
                    padding: 0;
                    box-sizing: border-box;
                }

                body {
                    font-family: 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
                    font-size: 18px;
                    background: white;
                    margin: 0;
                    padding: 0;
                    width: 100%;
                    height: 100%;
                }
                
                .container {
                    width: 100%;
                    background: white;
                    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
                    overflow: hidden;
                    display: block;
                    margin: 0 auto;
                }

                .header {
                    background: linear-gradient(135deg, #ff6b6b, #feca57);
                    color: white;
                    padding: 40px 30px;
                    text-align: center;
                    position: relative;
                }

                .header::before {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="40" r="3" fill="rgba(255,255,255,0.1)"/><circle cx="40" cy="80" r="2" fill="rgba(255,255,255,0.1)"/></svg>');
                }

                .header h1 {
                    font-size: 2.8em;
                    margin-bottom: 10px;
                    position: relative;
                    z-index: 1;
                }

                .header .subtitle {
                    font-size: 1.3em;
                    opacity: 0.9;
                    position: relative;
                    z-index: 1;
                }

                .content {
                    padding: 50px 40px;
                }

                .section {
                    margin-bottom: 45px;
                }

                .section-title {
                    font-size: 1.5em;
                    color: #2c3e50;
                    margin-bottom: 20px;
                    padding-bottom: 10px;
                    border-bottom: 3px solid #3498db;
                    position: relative;
                }

                .section-title::after {
                    content: '';
                    position: absolute;
                    bottom: -3px;
                    left: 0;
                    width: 50px;
                    height: 3px;
                    background: #e74c3c;
                }

                .command-grid {
                    display: grid;
                    grid-template-columns: repeat(3, 1fr);
                    gap: 25px;
                    margin-top: 25px;
                }

                .command-card {
                    background: #f8f9fa;
                    border-radius: 12px;
                    padding: 25px;
                    border-left: 4px solid #3498db;
                    transition: all 0.3s ease;
                    position: relative;
                    overflow: hidden;
                }

                .command-card::before {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    background: linear-gradient(45deg, transparent 30%, rgba(52, 152, 219, 0.05) 50%, transparent 70%);
                    transform: translateX(-100%);
                    transition: transform 0.6s ease;
                }

                .command-card:hover::before {
                    transform: translateX(100%);
                }

                .command-card:hover {
                    transform: translateY(-2px);
                    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
                }

                .command-name {
                    font-size: 1.3em;
                    font-weight: bold;
                    color: #2c3e50;
                    margin-bottom: 10px;
                    position: relative;
                    z-index: 1;
                }

                .command-desc {
                    color: #7f8c8d;
                    line-height: 1.6;
                    font-size: 1.0em;
                    position: relative;
                    z-index: 1;
                }

                .admin-section .command-card {
                    border-left-color: #e74c3c;
                }

                .admin-section .command-card::before {
                    background: linear-gradient(45deg, transparent 30%, rgba(231, 76, 60, 0.05) 50%, transparent 70%);
                }

                .usage-tips {
                    background: linear-gradient(135deg, #74b9ff, #0984e3);
                    color: white;
                    padding: 30px;
                    border-radius: 15px;
                    margin-top: 35px;
                }

                .usage-tips h3 {
                    margin-bottom: 15px;
                    font-size: 1.3em;
                }

                .usage-tips ul {
                    list-style: none;
                    padding-left: 0;
                }

                .usage-tips li {
                    margin-bottom: 10px;
                    padding-left: 20px;
                    position: relative;
                    font-size: 1.0em;
                    line-height: 1.4;
                }

                .usage-tips li::before {
                    content: '💡';
                    position: absolute;
                    left: 0;
                    top: 0;
                }

                .footer {
                    text-align: center;
                    padding: 20px;
                    background: #f1f2f6;
                    color: #7f8c8d;
                    font-size: 0.9em;
                }

                .emoji {
                    font-size: 1.2em;
                    margin-right: 8px;
                }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>🎭 Meme 表情包插件</h1>
                    <div class="subtitle">高性能表情包生成器 - 让聊天更有趣！</div>
                </div>

                <div class="content">
                    <div class="section">
                        <h2 class="section-title">📋 基础命令</h2>
                        <div class="command-grid">
                            {% for cmd in basic_commands %}
                            <div class="command-card">
                                <div class="command-name">
                                    <span class="emoji">{{ cmd.emoji }}</span>{{ cmd.name }}
                                </div>
                                <div class="command-desc">{{ cmd.desc }}</div>
                            </div>
                            {% endfor %}
                        </div>
                    </div>

                    <div class="section admin-section">
                        <h2 class="section-title">🔧 管理员命令</h2>
                        <div class="command-grid">
                            {% for cmd in admin_commands %}
                            <div class="command-card">
                                <div class="command-name">
                                    <span class="emoji">{{ cmd.emoji }}</span>{{ cmd.name }}
                                </div>
                                <div class="command-desc">{{ cmd.desc }}</div>
                            </div>
                            {% endfor %}
                        </div>
                    </div>

                    <div class="usage-tips">
                        <h3>💡 使用小贴士</h3>
                        <ul>
                            <li>直接发送表情包关键词即可生成，如："摸头"、"拍拍"</li>
                            <li>支持@用户自动获取头像，也可以上传图片</li>
                            <li>可以引用他人消息来使用其头像和昵称</li>
                            <li>命令前缀默认为/</li>
                        </ul>
                    </div>
                </div>

                <div class="footer">
                    <p>🌟 AstrBot Meme Generator Plugin v1.1.0 by SodaSizzle</p>
                </div>
            </div>
        </body>
        </html>
        '''

        # 准备模板数据
        template_data = {
            "basic_commands": [
                {
                    "emoji": "📝",
                    "name": "/表情列表",
                    "desc": "查看所有可用的表情包模板（别名：/图片列表、/模板列表）"
                },
                {
                    "emoji": "🔍",
                    "name": "/模板信息 <关键词>",
                    "desc": "查看指定表情包模板的详细信息和使用方法（别名：/表情信息、/查看模板）"
                },
                {
                    "emoji": "🎨",
                    "name": "直接发送关键词",
                    "desc": "发送表情包关键词即可生成，如：摸头、拍拍、举牌等"
                }
            ],
            "admin_commands": [
                {
                    "emoji": "🚫",
                    "name": "/禁用模板 <模板名>",
                    "desc": "禁用指定的表情包模板，禁用后用户无法使用"
                },
                {
                    "emoji": "✅",
                    "name": "/启用模板 <模板名>",
                    "desc": "重新启用被禁用的表情包模板"
                },
                {
                    "emoji": "📋",
                    "name": "/禁用列表",
                    "desc": "查看当前被禁用的模板列表"
                },
                {
                    "emoji": "🔧",
                    "name": "/启用表情包",
                    "desc": "启用整个表情包插件功能（别名：/开启表情包、/启用插件）"
                },
                {
                    "emoji": "⛔",
                    "name": "/禁用表情包",
                    "desc": "禁用整个表情包插件功能（别名：/关闭表情包、/禁用插件）"
                },
                {
                    "emoji": "📊",
                    "name": "/表情包状态",
                    "desc": "查看表情包插件当前状态（别名：/插件状态）"
                },
                {
                    "emoji": "ℹ️",
                    "name": "/表情包信息",
                    "desc": "查看表情包插件详细信息和统计（别名：/插件信息）"
                }
            ]
        }

        # 使用 html_render 方法渲染模板
        url = await self.html_render(MEME_HELP_TMPL, template_data)
        yield event.image_result(url)

    @filter.command("表情列表", alias={"图片列表", "模板列表"})
    async def template_list(self, event: AstrMessageEvent):
        """查看所有可用的表情包模板"""
        # 检查插件是否启用
        if not self.meme_config.is_plugin_enabled():
            if PermissionUtils.is_bot_admin(event):
                yield event.plain_result(PermissionUtils.get_plugin_disabled_message())
            return

        async for result in self.template_handlers.handle_template_list(event):
            yield result


    @filter.command("模板信息", alias={"表情信息", "查看模板"})
    async def template_info(
        self, event: AstrMessageEvent, keyword: str | int | None = None
    ):
        """查看指定表情包模板的详细信息"""
        # 检查插件是否启用
        if not self.meme_config.is_plugin_enabled():
            if PermissionUtils.is_bot_admin(event):
                yield event.plain_result(PermissionUtils.get_plugin_disabled_message())
            return

        async for result in self.template_handlers.handle_template_info(event, keyword):
            yield result

    @filter.permission_type(filter.PermissionType.ADMIN)
    @filter.command("禁用模板")
    async def disable_template(
        self, event: AstrMessageEvent, template_name: str | None = None
    ):
        """禁用指定的表情包模板（仅限Bot管理员）"""
        async for result in self.template_handlers.handle_disable_template(event, template_name):
            yield result

    @filter.permission_type(filter.PermissionType.ADMIN)
    @filter.command("启用模板")
    async def enable_template(
        self, event: AstrMessageEvent, template_name: str | None = None
    ):
        """启用指定的表情包模板（仅限Bot管理员）"""
        async for result in self.template_handlers.handle_enable_template(event, template_name):
            yield result

    @filter.permission_type(filter.PermissionType.ADMIN)
    @filter.command("禁用列表")
    async def list_disabled(self, event: AstrMessageEvent):
        """查看被禁用的模板列表（仅限Bot管理员）"""
        async for result in self.template_handlers.handle_list_disabled(event):
            yield result

    @filter.permission_type(filter.PermissionType.ADMIN)
    @filter.command("启用表情包", alias={"开启表情包", "启用插件"})
    async def enable_plugin(self, event: AstrMessageEvent):
        """启用表情包生成功能（仅限Bot管理员）"""
        async for result in self.admin_handlers.handle_enable_plugin(event):
            yield result

    @filter.permission_type(filter.PermissionType.ADMIN)
    @filter.command("禁用表情包", alias={"关闭表情包", "禁用插件"})
    async def disable_plugin(self, event: AstrMessageEvent):
        """禁用表情包生成功能（仅限Bot管理员）"""
        async for result in self.admin_handlers.handle_disable_plugin(event):
            yield result

    @filter.permission_type(filter.PermissionType.ADMIN)
    @filter.command("表情包状态", alias={"插件状态"})
    async def plugin_status(self, event: AstrMessageEvent):
        """查看表情包插件状态（仅限Bot管理员）"""
        async for result in self.admin_handlers.handle_plugin_status(event):
            yield result

    @filter.permission_type(filter.PermissionType.ADMIN)
    @filter.command("表情包信息", alias={"插件信息"})
    async def plugin_info(self, event: AstrMessageEvent):
        """查看表情包插件详细信息（仅限Bot管理员）"""
        async for result in self.admin_handlers.handle_plugin_info(event):
            yield result





    @filter.event_message_type(EventMessageType.ALL)
    async def generate_meme(self, event: AstrMessageEvent):
        """
        表情包生成主流程处理器。

        功能特性：
        - 关键词匹配和识别
        - 多源参数提取（消息文本、@用户、上传图片）
        - 引用消息内容解析
        - 自动补全缺失参数（头像、昵称等）
        """
        # 检查插件是否启用
        if not self.meme_config.is_plugin_enabled():
            # 插件被禁用时不响应普通用户，但Bot管理员可以看到提示
            if PermissionUtils.is_bot_admin(event):
                yield event.plain_result(PermissionUtils.get_plugin_disabled_message())
            return

        async for result in self.generation_handler.handle_generate_meme(event):
            yield result


