<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ meme.name }} - 模板详情</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            color: #333;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 40px 30px;
            text-align: center;
            position: relative;
        }
        
        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.1)"/><circle cx="10" cy="60" r="0.5" fill="rgba(255,255,255,0.1)"/><circle cx="90" cy="40" r="0.5" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
        }
        
        .header-content {
            position: relative;
            z-index: 1;
        }
        
        .meme-icon {
            width: 80px;
            height: 80px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            font-size: 2em;
            backdrop-filter: blur(10px);
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header .subtitle {
            font-size: 1.1em;
            opacity: 0.9;
        }
        
        .content {
            padding: 40px 30px;
        }
        
        .info-section {
            margin-bottom: 30px;
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
            border-left: 4px solid #4facfe;
        }
        
        .section-title {
            font-size: 1.3em;
            font-weight: bold;
            color: #333;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .section-icon {
            width: 24px;
            height: 24px;
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 12px;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .info-item {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 12px;
            text-align: center;
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
        }
        
        .info-item:hover {
            border-color: #4facfe;
            transform: translateY(-2px);
        }
        
        .info-value {
            font-size: 1.5em;
            font-weight: bold;
            color: #4facfe;
            display: block;
            margin-bottom: 5px;
        }
        
        .info-label {
            color: #666;
            font-size: 0.9em;
        }
        
        .keywords-list {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-top: 10px;
        }
        
        .keyword {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 0.9em;
            font-weight: 500;
        }
        
        .tags-list {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-top: 10px;
        }
        
        .tag {
            background: #f0f8ff;
            color: #4facfe;
            padding: 6px 12px;
            border-radius: 15px;
            font-size: 0.85em;
            border: 1px solid #e0f2ff;
        }
        
        .default-texts {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 10px;
            padding: 15px;
            margin-top: 10px;
        }
        
        .default-text-item {
            background: white;
            padding: 10px 15px;
            border-radius: 8px;
            margin: 5px 0;
            border-left: 3px solid #f39c12;
            font-family: 'Courier New', monospace;
        }
        
        .usage-example {
            background: #e8f5e8;
            border: 1px solid #c3e6c3;
            border-radius: 10px;
            padding: 20px;
            margin-top: 15px;
        }
        
        .example-title {
            font-weight: bold;
            color: #2d5a2d;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .example-code {
            background: white;
            padding: 12px 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            border-left: 3px solid #28a745;
            margin: 8px 0;
        }
        
        .footer {
            text-align: center;
            padding: 30px;
            background: #f8f9fa;
            color: #666;
        }
        
        .back-btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 12px 24px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
            margin-bottom: 15px;
        }
        
        .back-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(79, 172, 254, 0.3);
        }
        
        @media (max-width: 768px) {
            .container {
                margin: 10px;
            }
            
            .content {
                padding: 20px 15px;
            }
            
            .header {
                padding: 30px 20px;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .info-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="header-content">
                <div class="meme-icon">🎭</div>
                <h1>{{ meme.name }}</h1>
                <p class="subtitle">表情包模板详细信息</p>
            </div>
        </div>
        
        <div class="content">
            <div class="info-section">
                <div class="section-title">
                    <span class="section-icon">📝</span>
                    基本信息
                </div>
                <div class="info-grid">
                    <div class="info-item">
                        <span class="info-value">{{ meme.min_images }}{% if meme.min_images != meme.max_images %}-{{ meme.max_images }}{% endif %}</span>
                        <span class="info-label">所需图片数量</span>
                    </div>
                    <div class="info-item">
                        <span class="info-value">{{ meme.min_texts }}{% if meme.min_texts != meme.max_texts %}-{{ meme.max_texts }}{% endif %}</span>
                        <span class="info-label">所需文本数量</span>
                    </div>
                </div>
            </div>
            
            <div class="info-section">
                <div class="section-title">
                    <span class="section-icon">🔑</span>
                    触发关键词
                </div>
                <div class="keywords-list">
                    {% for keyword in meme.keywords %}
                    <span class="keyword">{{ keyword }}</span>
                    {% endfor %}
                </div>
            </div>
            
            {% if meme.tags %}
            <div class="info-section">
                <div class="section-title">
                    <span class="section-icon">🏷️</span>
                    标签分类
                </div>
                <div class="tags-list">
                    {% for tag in meme.tags %}
                    <span class="tag">{{ tag }}</span>
                    {% endfor %}
                </div>
            </div>
            {% endif %}
            
            {% if meme.default_texts %}
            <div class="info-section">
                <div class="section-title">
                    <span class="section-icon">💬</span>
                    默认文本
                </div>
                <div class="default-texts">
                    {% for text in meme.default_texts %}
                    <div class="default-text-item">{{ text }}</div>
                    {% endfor %}
                </div>
            </div>
            {% endif %}
            
            <div class="info-section">
                <div class="section-title">
                    <span class="section-icon">💡</span>
                    使用示例
                </div>
                <div class="usage-example">
                    <div class="example-title">
                        <span>📋</span>
                        基本用法
                    </div>
                    {% if meme.min_images > 0 %}
                    <div class="example-code">{{ meme.keywords[0] }} [上传{{ meme.min_images }}张图片]{% if meme.min_texts > 0 %} 文本内容{% endif %}</div>
                    {% else %}
                    <div class="example-code">{{ meme.keywords[0] }}{% if meme.min_texts > 0 %} 文本内容{% endif %}</div>
                    {% endif %}
                    
                    {% if meme.min_texts > 1 %}
                    <div class="example-title">
                        <span>📝</span>
                        多段文本用法
                    </div>
                    <div class="example-code">{{ meme.keywords[0] }}{% if meme.min_images > 0 %} [上传图片]{% endif %} 第一段文本 第二段文本</div>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <div class="footer">
            <a href="#" class="back-btn">
                <span>←</span>
                返回模板列表
            </a>
            <p>💡 发送上述格式的消息即可生成对应的表情包</p>
            <p>🔍 使用 "/表情列表" 查看所有可用模板</p>
        </div>
    </div>
</body>
</html>
