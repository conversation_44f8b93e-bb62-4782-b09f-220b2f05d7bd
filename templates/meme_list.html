<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>表情包模板列表</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            color: #333;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
            position: relative;
        }
        
        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.1)"/><circle cx="10" cy="60" r="0.5" fill="rgba(255,255,255,0.1)"/><circle cx="90" cy="40" r="0.5" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            position: relative;
            z-index: 1;
        }
        
        .header .subtitle {
            font-size: 1.1em;
            opacity: 0.9;
            position: relative;
            z-index: 1;
        }
        
        .stats {
            display: flex;
            justify-content: center;
            gap: 30px;
            margin-top: 20px;
            position: relative;
            z-index: 1;
        }
        
        .stat-item {
            text-align: center;
        }
        
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            display: block;
        }
        
        .stat-label {
            font-size: 0.9em;
            opacity: 0.8;
        }
        
        .content {
            padding: 30px;
        }
        
        .category-info {
            margin-bottom: 30px;
            text-align: center;
        }
        
        .meme-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .meme-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
            transition: all 0.3s ease;
            border: 2px solid transparent;
            position: relative;
            overflow: hidden;
        }
        
        .meme-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #4facfe, #00f2fe);
        }
        
        .meme-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
            border-color: #4facfe;
        }
        
        .meme-name {
            font-size: 1.2em;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .meme-icon {
            width: 20px;
            height: 20px;
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 12px;
        }
        
        .meme-keywords {
            color: #666;
            font-size: 0.9em;
            margin-bottom: 15px;
            line-height: 1.4;
        }
        
        .meme-info {
            display: flex;
            justify-content: space-between;
            gap: 15px;
            margin-bottom: 15px;
        }
        
        .info-item {
            text-align: center;
            flex: 1;
        }
        
        .info-number {
            font-size: 1.1em;
            font-weight: bold;
            color: #4facfe;
            display: block;
        }
        
        .info-label {
            font-size: 0.8em;
            color: #999;
            margin-top: 2px;
        }
        
        .meme-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 5px;
        }
        
        .tag {
            background: #f0f8ff;
            color: #4facfe;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            border: 1px solid #e0f2ff;
        }
        
        .footer {
            text-align: center;
            padding: 20px;
            background: #f8f9fa;
            color: #666;
            font-size: 0.9em;
        }
        
        .pagination {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin-top: 20px;
        }
        
        .page-btn {
            padding: 8px 15px;
            background: #f5f5f5;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .page-btn.active {
            background: #4facfe;
            color: white;
        }
        
        @media (max-width: 768px) {
            .meme-grid {
                grid-template-columns: 1fr;
            }
            
            .stats {
                gap: 15px;
            }
            
            .stat-number {
                font-size: 1.5em;
            }
            
            .header h1 {
                font-size: 2em;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎭 表情包模板列表</h1>
            <p class="subtitle">发现更多有趣的表情包模板</p>
            <div class="stats">
                <div class="stat-item">
                    <span class="stat-number">{{ total_memes }}</span>
                    <span class="stat-label">总模板数</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">{{ categories|length }}</span>
                    <span class="stat-label">分类数</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">{{ enabled_memes }}</span>
                    <span class="stat-label">可用模板</span>
                </div>
            </div>
        </div>
        
        <div class="content">
            <div class="category-info">
                <p style="text-align: center; color: #666; margin-bottom: 30px;">
                    📂 包含分类: {{ categories|join(', ') if categories else '无分类' }}
                </p>
            </div>
            
            <div class="meme-grid" id="memeGrid">
                {% for meme in memes %}
                <div class="meme-card" data-keywords="{{ meme.keywords|join(' ') }}" data-category="{{ meme.tags|join(' ') }}">
                    <div class="meme-name">
                        <span class="meme-icon">{{ loop.index }}</span>
                        {{ meme.name }}
                    </div>
                    <div class="meme-keywords">
                        <strong>关键词:</strong> {{ meme.keywords|join(', ') }}
                    </div>
                    <div class="meme-info">
                        <div class="info-item">
                            <span class="info-number">{{ meme.min_images }}{% if meme.min_images != meme.max_images %}-{{ meme.max_images }}{% endif %}</span>
                            <span class="info-label">图片</span>
                        </div>
                        <div class="info-item">
                            <span class="info-number">{{ meme.min_texts }}{% if meme.min_texts != meme.max_texts %}-{{ meme.max_texts }}{% endif %}</span>
                            <span class="info-label">文本</span>
                        </div>
                    </div>
                    {% if meme.tags %}
                    <div class="meme-tags">
                        {% for tag in meme.tags %}
                        <span class="tag">{{ tag }}</span>
                        {% endfor %}
                    </div>
                    {% endif %}
                </div>
                {% endfor %}
            </div>
        </div>
        
        <div class="footer">
            <p>💡 使用方式: 发送 "关键词 + 图片/文字" 即可生成表情包</p>
            <p>📝 查看详情: 发送 "/模板信息 关键词" 查看具体使用方法</p>
        </div>
    </div>


</body>
</html>
