"""管理员命令处理器"""

from astrbot.core.platform import AstrMessageEvent
from ..config import MemeConfig


class AdminHandlers:
    """管理员命令处理器"""
    
    def __init__(self, config: MemeConfig):
        self.config = config
    
    async def handle_enable_plugin(self, event: AstrMessageEvent):
        """处理启用插件命令"""
        # 尝试启用插件
        if self.config.enable_plugin_func():
            yield event.plain_result("✅ 表情包生成功能已启用")
        else:
            yield event.plain_result("ℹ️ 表情包生成功能已经是启用状态")

    async def handle_disable_plugin(self, event: AstrMessageEvent):
        """处理禁用插件命令"""
        # 尝试禁用插件
        if self.config.disable_plugin_func():
            yield event.plain_result("🔒 表情包生成功能已禁用")
        else:
            yield event.plain_result("ℹ️ 表情包生成功能已经是禁用状态")

    async def handle_plugin_status(self, event: AstrMessageEvent):
        """处理查看插件状态命令"""
        # 获取插件状态
        status = "启用" if self.config.is_plugin_enabled() else "禁用"
        status_emoji = "✅" if self.config.is_plugin_enabled() else "🔒"

        yield event.plain_result(f"{status_emoji} 表情包生成功能当前状态: {status}")

    async def handle_plugin_info(self, event: AstrMessageEvent):
        """处理查看插件信息命令"""
        # 构建插件信息
        info_text = "📊 表情包生成器插件信息\n"
        info_text += f"状态: {'✅ 启用' if self.config.is_plugin_enabled() else '🔒 禁用'}\n"

        info_text += f"头像缓存: {'✅ 开启' if self.config.enable_avatar_cache else '❌ 关闭'}\n"

        info_text += f"冷却时间: {self.config.cooldown_seconds}秒\n"
        info_text += f"生成超时: {self.config.generation_timeout}秒\n"
        info_text += f"缓存过期: {self.config.cache_expire_hours}小时\n"

        info_text += f"禁用模板: {len(self.config.disabled_templates)}个"

        yield event.plain_result(info_text)




